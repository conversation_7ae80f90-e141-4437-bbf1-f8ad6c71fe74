package templates

import (
	"github.com/nickabs/demo/internal/store"
)

templ BaseLayout(title string) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title }</title>
			<script src="https://unpkg.com/htmx.org@2.0.4"></script>
			<link href="/static/css/app.css" rel="stylesheet"/>
		</head>
		<body>
			{ children... }	
		</body>
	</html>
}

templ RegisterPage() {
	@BaseLayout("register") {
		<form> 
			name: <input type="text" name="name" autocomplete="given-name"/>
			email <input type="email" name="email" autocomplete="email"/>
			<button id="submit"
				hx-post="/register"
				hx-target="#summary"
				hx-swap="innerHTML"
			>submit</button>
			<button id="clear" 
				hx-post="/clear"
				hx-target="#summary"
				hx-swap="outerHTML"
			>clear</button>
		</form>
		<br>
		<h3>Stored Contacts</h3>
	}
}

templ RenderStoredContacts(userStore *store.UserStore) {
	<div id="summary">
	<br> 
	<ul>
		for _,user := range userStore.GetUsers() {
			<li> { user.Email } | { user.Name }</li>
		}
	</ul>
	</div>

}
