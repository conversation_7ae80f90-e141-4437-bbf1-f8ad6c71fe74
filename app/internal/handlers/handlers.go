package handlers

import (
	"github.com/labstack/echo/v4"
	"github.com/nickabs/demo/internal/store"
	"github.com/nickabs/demo/internal/templates"
)

func RegisterPageHandler(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)
	res := c.Response()
	req := c.Request()
	ctx := req.Context()

	templates.RegisterPage().Render(ctx, res.Writer)
	templates.RenderStoredContacts(userStore).Render(ctx, res.Writer)

	return nil
}

func Clear(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)

	userStore.ClearData()

	c.Response().Write([]byte(`<div id="summary"> no contacts available</div>`))

	return nil
}
func Register(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)
	req := c.Request()
	res := c.Response()
	ctx := req.Context()

	name := req.FormValue("name")
	email := req.FormValue("email")

	userStore.AddUser(name, email)

	templates.RenderStoredContacts(userStore).Render(ctx, res.Writer)
	return nil
}
