package store

type User struct {
	Name  string
	Email string
}

type UserStore struct {
	Users []User
}

func (u *UserStore) AddUser(name, email string) {
	u.Users = append(u.Users, User{name, email})
}

func (u *UserStore) GetUsers() []User {
	return u.Users
}

func (u *UserStore) ClearData() {
	u = &UserStore{
		Users: make([]User, 0),
	}
}

func (u *UserStore) InitData() {
	u.AddUser("bob", "<EMAIL>")
	u.AddUser("jane", "<EMAIL>")
}
